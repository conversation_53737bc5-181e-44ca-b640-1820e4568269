# 多网站部署指南

本文档详细说明如何部署和管理多个网站项目。

## 🏗️ 部署架构说明

### 部署方式选择

**推荐方式：本地构建 + 服务器部署**

1. **本地构建**：在开发机器上执行 `npm run build` 生成生产版本
2. **上传构建产物**：将 `dist` 目录上传到服务器
3. **服务器运行**：使用 `npm run preview` 运行生产版本

**备选方式：服务器直接构建**

- 将源码上传到服务器，在服务器上执行构建
- 适合服务器配置较高的情况

### 目录结构详解

#### 开发环境目录结构

```
本地开发机器：
moevend-website/                    # 项目根目录
├── sites/                          # 网站源码目录
│   ├── moevend/                    # 萌贩机网站源码
│   │   ├── src/                    # Vue源码
│   │   ├── dist/                   # 构建产物 (npm run build后生成)
│   │   ├── package.json            # 依赖配置
│   │   └── vite.config.ts          # 构建配置 (端口: 3001)
│   ├── graphock/                   # Graphock网站源码
│   │   ├── src/                    # Vue源码
│   │   ├── dist/                   # 构建产物 (npm run build后生成)
│   │   ├── package.json            # 依赖配置
│   │   └── vite.config.ts          # 构建配置 (端口: 3002)
│   └── example-site/               # 示例网站模板
├── nginx/                          # Nginx配置文件 (需要复制到服务器)
│   ├── nginx.conf                  # 主配置文件
│   └── sites-available/            # 站点配置目录
│       ├── moevend.conf            # 萌贩机网站配置
│       └── graphock.conf           # Graphock网站配置
├── pm2/                            # PM2配置文件 (需要复制到服务器)
│   ├── ecosystem.config.js         # 生产环境进程配置
│   └── ecosystem.dev.config.js     # 开发环境进程配置
├── scripts/                        # 部署脚本 (需要复制到服务器)
│   ├── server-init.sh              # 服务器初始化脚本
│   ├── deploy.sh                   # 自动部署脚本
│   └── site-manager.sh             # 站点管理脚本
└── logs/                           # 日志目录 (服务器上创建)
```

#### 服务器目录结构

```
Linux服务器：
/var/www/moevend-website/           # 项目部署目录
├── sites/                          # 网站运行目录
│   ├── moevend/                    # 萌贩机网站
│   │   ├── dist/                   # 构建产物 (从本地上传)
│   │   ├── node_modules/           # 运行时依赖
│   │   ├── package.json            # 依赖配置
│   │   └── vite.config.ts          # 配置文件
│   └── graphock/                   # Graphock网站
│       ├── dist/                   # 构建产物 (从本地上传)
│       ├── node_modules/           # 运行时依赖
│       ├── package.json            # 依赖配置
│       └── vite.config.ts          # 配置文件
├── pm2/                            # PM2配置
├── scripts/                        # 管理脚本
└── logs/                           # 运行日志

/etc/nginx/                         # Nginx系统目录
├── nginx.conf                      # 主配置 (替换系统默认)
├── sites-available/                # 可用站点配置
│   ├── moevend.conf               # 萌贩机站点配置
│   └── graphock.conf              # Graphock站点配置
└── sites-enabled/                  # 启用站点配置 (软链接)
    ├── moevend.conf -> ../sites-available/moevend.conf
    └── graphock.conf -> ../sites-available/graphock.conf

/var/log/nginx/                     # Nginx日志目录
├── access.log                      # 访问日志
├── error.log                       # 错误日志
├── moevend_access.log             # 萌贩机访问日志
├── moevend_error.log              # 萌贩机错误日志
├── graphock_access.log            # Graphock访问日志
└── graphock_error.log             # Graphock错误日志
```

#### 网络流程图

```
用户请求 → Nginx (端口80/443) → PM2进程 → Vue应用

具体流程：
1. 用户访问 moevend.cn
   ↓
2. Nginx接收请求 (端口443)
   ↓
3. Nginx反向代理到 localhost:3001
   ↓
4. PM2管理的moevend进程响应
   ↓
5. Vue应用返回页面内容

1. 用户访问 graphock.cn
   ↓
2. Nginx接收请求 (端口443)
   ↓
3. Nginx反向代理到 localhost:3002
   ↓
4. PM2管理的graphock进程响应
   ↓
5. Vue应用返回页面内容
```

## 服务器要求

- Ubuntu 20.04+ 或 CentOS 8+
- Node.js 20+
- Nginx 1.18+
- PM2 (全局安装)
- 至少 2GB RAM
- 至少 20GB 存储空间

## 📋 详细部署流程

### 方式一：推荐部署流程 (本地构建)

#### 步骤 1: 本地准备工作

```bash
# 在本地开发机器上
cd moevend-website

# 构建所有网站
cd sites/moevend
npm install
npm run build    # 生成 dist/ 目录

cd ../graphock
npm install
npm run build    # 生成 dist/ 目录

cd ../..
```

**说明**: 这一步在您的开发机器上执行，生成优化后的生产版本文件。

#### 步骤 2: 上传到服务器

```bash
# 方法1: 使用rsync上传 (推荐)
rsync -avz --exclude 'node_modules' --exclude 'src' \
  ./ user@your-server:/var/www/moevend-website/

# 方法2: 使用scp上传
scp -r ./ user@your-server:/var/www/moevend-website/

# 方法3: 使用Git (如果代码已提交)
# 在服务器上执行:
git clone <repository-url> /var/www/moevend-website
```

**说明**: 将构建好的文件和配置上传到服务器，排除不必要的源码和依赖。

#### 步骤 3: 服务器初始化

```bash
# 在服务器上执行
cd /var/www/moevend-website
sudo ./scripts/server-init.sh
```

**说明**: 安装 Node.js、Nginx、PM2 等必要软件，创建用户和目录。

#### 步骤 4: 安装运行时依赖

```bash
# 只安装生产依赖，不需要开发依赖
cd /var/www/moevend-website/sites/moevend
npm install --production

cd ../graphock
npm install --production
```

**说明**: 安装运行 `npm run preview` 所需的最小依赖包。

#### 步骤 5: 配置域名和 SSL

```bash
# 配置域名DNS解析 (在域名服务商后台)
# moevend.cn -> 服务器IP
# graphock.cn -> 服务器IP

# 安装SSL证书 (使用Let's Encrypt)
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d moevend.cn -d www.moevend.cn
sudo certbot --nginx -d graphock.cn -d www.graphock.cn
```

**说明**: 配置域名解析和 HTTPS 证书，确保网站可以通过域名安全访问。

#### 步骤 6: 启动服务

```bash
# 复制Nginx配置
sudo cp nginx/sites-available/*.conf /etc/nginx/sites-available/
sudo ln -sf /etc/nginx/sites-available/moevend.conf /etc/nginx/sites-enabled/
sudo ln -sf /etc/nginx/sites-available/graphock.conf /etc/nginx/sites-enabled/

# 测试并重启Nginx
sudo nginx -t
sudo systemctl restart nginx

# 启动PM2服务
pm2 start pm2/ecosystem.config.js --env production
pm2 save
pm2 startup
```

**说明**: 配置反向代理，启动网站进程，设置开机自启动。

### 方式二：服务器直接构建 (备选)

#### 步骤 1: 上传源码

```bash
# 上传完整源码到服务器
rsync -avz ./ user@your-server:/var/www/moevend-website/
```

#### 步骤 2: 服务器构建

```bash
# 在服务器上执行构建
cd /var/www/moevend-website

# 构建所有网站
cd sites/moevend
npm install
npm run build

cd ../graphock
npm install
npm run build

cd ../..
```

#### 步骤 3: 后续步骤同方式一

按照方式一的步骤 3-6 继续执行。

## 🚀 快速部署总结

### 本地操作 (在您的开发机器上)

```bash
# 1. 构建项目
cd sites/moevend && npm install && npm run build
cd ../graphock && npm install && npm run build
cd ../..

# 2. 上传到服务器 (替换your-server为实际服务器地址)
rsync -avz --exclude 'node_modules' --exclude 'src' \
  ./ user@your-server:/var/www/moevend-website/
```

### 服务器操作 (在您的服务器上)

```bash
# 3. 初始化服务器环境
cd /var/www/moevend-website
sudo ./scripts/server-init.sh

# 4. 安装运行时依赖
cd sites/moevend && npm install --production
cd ../graphock && npm install --production
cd ../..

# 5. 配置并启动服务
sudo cp nginx/sites-available/*.conf /etc/nginx/sites-available/
sudo ln -sf /etc/nginx/sites-available/moevend.conf /etc/nginx/sites-enabled/
sudo ln -sf /etc/nginx/sites-available/graphock.conf /etc/nginx/sites-enabled/
sudo nginx -t && sudo systemctl restart nginx

pm2 start pm2/ecosystem.config.js --env production
pm2 save && pm2 startup
```

### 验证部署

```bash
# 检查服务状态
pm2 status
sudo systemctl status nginx

# 检查网站访问
curl -I http://localhost:3001  # 萌贩机网站
curl -I http://localhost:3002  # Graphock网站
```

## 📁 关键目录说明

### 每个步骤操作的目录

| 步骤       | 操作位置                 | 目录作用     | 生成内容                             |
| ---------- | ------------------------ | ------------ | ------------------------------------ |
| 本地构建   | `sites/moevend/`         | Vue 项目源码 | `dist/` 构建产物                     |
| 本地构建   | `sites/graphock/`        | Vue 项目源码 | `dist/` 构建产物                     |
| 上传文件   | 整个项目                 | 项目根目录   | 所有文件到服务器                     |
| Nginx 配置 | `nginx/sites-available/` | 站点配置文件 | 复制到 `/etc/nginx/sites-available/` |
| PM2 配置   | `pm2/`                   | 进程管理配置 | 启动网站进程                         |
| 运行网站   | `sites/*/dist/`          | 构建产物     | 通过 `npm run preview` 运行          |

### Nginx 配置文件作用

```bash
# 这些文件的作用：
nginx/sites-available/moevend.conf   → 萌贩机网站反向代理配置
nginx/sites-available/graphock.conf  → Graphock网站反向代理配置
nginx/nginx.conf                     → Nginx主配置文件

# 复制到系统目录后：
/etc/nginx/sites-available/moevend.conf   → 可用配置
/etc/nginx/sites-enabled/moevend.conf     → 启用配置 (软链接)
```

## 详细部署步骤

### 1. 环境准备

#### 安装 Node.js

```bash
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt install -y nodejs
```

#### 安装 PM2

```bash
sudo npm install -g pm2
```

#### 安装 Nginx

```bash
sudo apt install -y nginx
sudo systemctl start nginx
sudo systemctl enable nginx
```

### 2. 项目配置

#### 安装依赖

```bash
# Moevend网站
cd sites/moevend
npm install
npm run build

# Graphock网站
cd ../graphock
npm install
npm run build
```

#### 配置 Nginx

```bash
# 复制配置文件
sudo cp nginx/sites-available/*.conf /etc/nginx/sites-available/

# 启用站点
sudo ln -s /etc/nginx/sites-available/moevend.conf /etc/nginx/sites-enabled/
sudo ln -s /etc/nginx/sites-available/graphock.conf /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重新加载
sudo systemctl reload nginx
```

#### 启动 PM2 服务

```bash
# 生产环境
pm2 start pm2/ecosystem.config.js --env production

# 开发环境
pm2 start pm2/ecosystem.dev.config.js

# 保存PM2配置
pm2 save

# 设置开机启动
pm2 startup
```

## 站点管理

使用站点管理脚本进行日常操作：

```bash
# 查看帮助
./scripts/site-manager.sh help

# 启动所有站点
./scripts/site-manager.sh start all

# 启动特定站点
./scripts/site-manager.sh start moevend

# 停止站点
./scripts/site-manager.sh stop graphock

# 重启站点
./scripts/site-manager.sh restart all

# 查看状态
./scripts/site-manager.sh status

# 查看日志
./scripts/site-manager.sh logs moevend

# 构建站点
./scripts/site-manager.sh build all

# 开发模式
./scripts/site-manager.sh dev moevend
```

## 添加新网站

### 1. 创建新网站项目

```bash
# 复制示例模板
cp -r sites/example-site sites/new-site

# 进入新站点目录
cd sites/new-site

# 修改package.json中的项目名称
# 修改vite.config.ts中的端口号 (例如: 3004)
```

### 2. 配置 Nginx

```bash
# 复制并修改配置文件
cp nginx/sites-available/moevend.conf nginx/sites-available/new-site.conf

# 修改域名和端口
# server_name new-site.com www.new-site.com;
# proxy_pass http://127.0.0.1:3004;
```

### 3. 更新 PM2 配置

在 `pm2/ecosystem.config.js` 中添加新应用配置：

```javascript
{
  name: 'new-site-website',
  script: 'npm',
  args: 'run preview',
  cwd: './sites/new-site',
  instances: 1,
  autorestart: true,
  watch: false,
  max_memory_restart: '1G',
  env: {
    NODE_ENV: 'production',
    PORT: 3004
  }
}
```

### 4. 部署新站点

```bash
# 构建新站点
cd sites/new-site
npm install
npm run build

# 启用Nginx配置
sudo ln -s /etc/nginx/sites-available/new-site.conf /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx

# 启动PM2应用
pm2 start pm2/ecosystem.config.js --only new-site-website --env production
pm2 save
```

## 监控和维护

### 日志管理

```bash
# PM2日志
pm2 logs

# Nginx日志
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# 站点特定日志
tail -f logs/moevend-combined.log
```

### 性能监控

```bash
# PM2监控
pm2 monit

# 系统资源
htop
df -h
free -h
```

### 备份策略

```bash
# 自动备份脚本 (添加到crontab)
0 2 * * * /var/www/moevend-website/scripts/backup.sh
```

## 故障排除

### 常见问题

1. **端口冲突**

   - 检查端口占用：`netstat -tlnp | grep :3001`
   - 修改 vite.config.ts 中的端口配置

2. **Nginx 配置错误**

   - 测试配置：`sudo nginx -t`
   - 检查语法错误和路径

3. **PM2 应用无法启动**

   - 检查日志：`pm2 logs`
   - 验证 Node.js 版本和依赖

4. **SSL 证书问题**
   - 续期证书：`sudo certbot renew`
   - 检查证书状态：`sudo certbot certificates`

### 性能优化

1. **启用 Gzip 压缩** (已在 nginx.conf 中配置)
2. **设置静态文件缓存** (已在站点配置中设置)
3. **使用 CDN** (可选)
4. **数据库优化** (如果使用数据库)

## 安全建议

1. **定期更新系统和软件包**
2. **配置防火墙规则**
3. **使用强密码和 SSH 密钥**
4. **定期备份数据**
5. **监控系统日志**
6. **限制文件权限**

## 联系支持

如有问题，请联系技术支持团队。
