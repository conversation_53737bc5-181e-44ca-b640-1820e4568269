# 多网站部署指南

本文档详细说明如何部署和管理多个网站项目。

## 项目结构

```
moevend-website/
├── sites/                    # 网站目录
│   ├── moevend/              # 萌贩机网站 (端口: 3001)
│   ├── graphock/             # Graphock网站 (端口: 3002)
│   └── example-site/         # 示例网站模板 (端口: 3003)
├── nginx/                    # Nginx配置
│   ├── nginx.conf            # 主配置文件
│   └── sites-available/      # 站点配置
├── pm2/                      # PM2配置
│   ├── ecosystem.config.js   # 生产环境配置
│   └── ecosystem.dev.config.js # 开发环境配置
├── scripts/                  # 部署脚本
│   ├── server-init.sh        # 服务器初始化
│   ├── deploy.sh             # 部署脚本
│   └── site-manager.sh       # 站点管理
└── logs/                     # 日志目录
```

## 服务器要求

- Ubuntu 20.04+ 或 CentOS 8+
- Node.js 20+
- Nginx 1.18+
- PM2 (全局安装)
- 至少 2GB RAM
- 至少 20GB 存储空间

## 快速部署

### 1. 服务器初始化

```bash
# 克隆项目
git clone <repository-url> /var/www/moevend-website
cd /var/www/moevend-website

# 运行服务器初始化脚本
sudo ./scripts/server-init.sh
```

### 2. 配置域名和 SSL

```bash
# 配置域名DNS解析
# moevend.cn -> 服务器IP
# graphock.cn -> 服务器IP

# 安装SSL证书 (使用Let's Encrypt)
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d moevend.com -d www.moevend.com
sudo certbot --nginx -d graphock.com -d www.graphock.com
```

### 3. 部署网站

```bash
# 运行部署脚本
./scripts/deploy.sh
```

## 详细部署步骤

### 1. 环境准备

#### 安装 Node.js

```bash
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt install -y nodejs
```

#### 安装 PM2

```bash
sudo npm install -g pm2
```

#### 安装 Nginx

```bash
sudo apt install -y nginx
sudo systemctl start nginx
sudo systemctl enable nginx
```

### 2. 项目配置

#### 安装依赖

```bash
# Moevend网站
cd sites/moevend
npm install
npm run build

# Graphock网站
cd ../graphock
npm install
npm run build
```

#### 配置 Nginx

```bash
# 复制配置文件
sudo cp nginx/sites-available/*.conf /etc/nginx/sites-available/

# 启用站点
sudo ln -s /etc/nginx/sites-available/moevend.conf /etc/nginx/sites-enabled/
sudo ln -s /etc/nginx/sites-available/graphock.conf /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重新加载
sudo systemctl reload nginx
```

#### 启动 PM2 服务

```bash
# 生产环境
pm2 start pm2/ecosystem.config.js --env production

# 开发环境
pm2 start pm2/ecosystem.dev.config.js

# 保存PM2配置
pm2 save

# 设置开机启动
pm2 startup
```

## 站点管理

使用站点管理脚本进行日常操作：

```bash
# 查看帮助
./scripts/site-manager.sh help

# 启动所有站点
./scripts/site-manager.sh start all

# 启动特定站点
./scripts/site-manager.sh start moevend

# 停止站点
./scripts/site-manager.sh stop graphock

# 重启站点
./scripts/site-manager.sh restart all

# 查看状态
./scripts/site-manager.sh status

# 查看日志
./scripts/site-manager.sh logs moevend

# 构建站点
./scripts/site-manager.sh build all

# 开发模式
./scripts/site-manager.sh dev moevend
```

## 添加新网站

### 1. 创建新网站项目

```bash
# 复制示例模板
cp -r sites/example-site sites/new-site

# 进入新站点目录
cd sites/new-site

# 修改package.json中的项目名称
# 修改vite.config.ts中的端口号 (例如: 3004)
```

### 2. 配置 Nginx

```bash
# 复制并修改配置文件
cp nginx/sites-available/moevend.conf nginx/sites-available/new-site.conf

# 修改域名和端口
# server_name new-site.com www.new-site.com;
# proxy_pass http://127.0.0.1:3004;
```

### 3. 更新 PM2 配置

在 `pm2/ecosystem.config.js` 中添加新应用配置：

```javascript
{
  name: 'new-site-website',
  script: 'npm',
  args: 'run preview',
  cwd: './sites/new-site',
  instances: 1,
  autorestart: true,
  watch: false,
  max_memory_restart: '1G',
  env: {
    NODE_ENV: 'production',
    PORT: 3004
  }
}
```

### 4. 部署新站点

```bash
# 构建新站点
cd sites/new-site
npm install
npm run build

# 启用Nginx配置
sudo ln -s /etc/nginx/sites-available/new-site.conf /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx

# 启动PM2应用
pm2 start pm2/ecosystem.config.js --only new-site-website --env production
pm2 save
```

## 监控和维护

### 日志管理

```bash
# PM2日志
pm2 logs

# Nginx日志
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# 站点特定日志
tail -f logs/moevend-combined.log
```

### 性能监控

```bash
# PM2监控
pm2 monit

# 系统资源
htop
df -h
free -h
```

### 备份策略

```bash
# 自动备份脚本 (添加到crontab)
0 2 * * * /var/www/moevend-website/scripts/backup.sh
```

## 故障排除

### 常见问题

1. **端口冲突**

   - 检查端口占用：`netstat -tlnp | grep :3001`
   - 修改 vite.config.ts 中的端口配置

2. **Nginx 配置错误**

   - 测试配置：`sudo nginx -t`
   - 检查语法错误和路径

3. **PM2 应用无法启动**

   - 检查日志：`pm2 logs`
   - 验证 Node.js 版本和依赖

4. **SSL 证书问题**
   - 续期证书：`sudo certbot renew`
   - 检查证书状态：`sudo certbot certificates`

### 性能优化

1. **启用 Gzip 压缩** (已在 nginx.conf 中配置)
2. **设置静态文件缓存** (已在站点配置中设置)
3. **使用 CDN** (可选)
4. **数据库优化** (如果使用数据库)

## 安全建议

1. **定期更新系统和软件包**
2. **配置防火墙规则**
3. **使用强密码和 SSH 密钥**
4. **定期备份数据**
5. **监控系统日志**
6. **限制文件权限**

## 联系支持

如有问题，请联系技术支持团队。
