<template>
  <div class="contact">
    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-primary-600 to-primary-700 text-white py-16">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl font-bold mb-4">联系我们</h1>
        <p class="text-xl opacity-90">
          我们期待与您的合作，共创美好未来
        </p>
      </div>
    </section>

    <!-- Contact Content -->
    <section class="py-16 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
          <!-- Contact Form -->
          <div>
            <h2 class="text-2xl font-bold text-gray-900 mb-6">发送消息</h2>
            <form @submit.prevent="submitForm" class="space-y-6">
              <div>
                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                  姓名 *
                </label>
                <input
                  type="text"
                  id="name"
                  v-model="form.name"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  placeholder="请输入您的姓名"
                />
              </div>
              
              <div>
                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                  邮箱 *
                </label>
                <input
                  type="email"
                  id="email"
                  v-model="form.email"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  placeholder="请输入您的邮箱"
                />
              </div>
              
              <div>
                <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                  电话
                </label>
                <input
                  type="tel"
                  id="phone"
                  v-model="form.phone"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  placeholder="请输入您的电话号码"
                />
              </div>
              
              <div>
                <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">
                  主题 *
                </label>
                <select
                  id="subject"
                  v-model="form.subject"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">请选择咨询主题</option>
                  <option value="潮玩商品">潮玩商品设计和销售</option>
                  <option value="日用百货">日用百货零售</option>
                  <option value="软件开发">软件开发和销售</option>
                  <option value="移动应用">移动端App开发</option>
                  <option value="无人售货机">无人售货机开发</option>
                  <option value="无人零售">无人零售</option>
                  <option value="其他">其他</option>
                </select>
              </div>
              
              <div>
                <label for="message" class="block text-sm font-medium text-gray-700 mb-2">
                  消息 *
                </label>
                <textarea
                  id="message"
                  v-model="form.message"
                  required
                  rows="5"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  placeholder="请详细描述您的需求..."
                ></textarea>
              </div>
              
              <button
                type="submit"
                class="w-full bg-primary-600 text-white py-3 px-4 rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors"
              >
                发送消息
              </button>
            </form>
          </div>

          <!-- Contact Information -->
          <div>
            <h2 class="text-2xl font-bold text-gray-900 mb-6">联系信息</h2>
            
            <div class="space-y-6">
              <!-- Company Info -->
              <div class="bg-gray-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">公司信息</h3>
                <div class="space-y-3">
                  <div class="flex items-start">
                    <span class="text-primary-600 mr-3 mt-1">🏢</span>
                    <div>
                      <p class="font-medium text-gray-900">杭州虹色萌机科技有限公司</p>
                      <p class="text-gray-600">Hangzhou Hongse Mengji Technology Co., Ltd.</p>
                    </div>
                  </div>
                  
                  <div class="flex items-start">
                    <span class="text-primary-600 mr-3 mt-1">📍</span>
                    <div>
                      <p class="font-medium text-gray-900">公司地址</p>
                      <p class="text-gray-600">浙江省杭州市</p>
                    </div>
                  </div>
                  
                  <div class="flex items-start">
                    <span class="text-primary-600 mr-3 mt-1">💼</span>
                    <div>
                      <p class="font-medium text-gray-900">营业范围</p>
                      <p class="text-gray-600">软件开发/销售、电商开发、售货机开发/销售、潮玩设计/销售、无人零售</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Business Hours -->
              <div class="bg-gray-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">营业时间</h3>
                <div class="space-y-2">
                  <div class="flex justify-between">
                    <span class="text-gray-600">周一 - 周五</span>
                    <span class="text-gray-900">9:00 - 18:00</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-600">周六</span>
                    <span class="text-gray-900">9:00 - 17:00</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-600">周日</span>
                    <span class="text-gray-900">休息</span>
                  </div>
                </div>
              </div>

              <!-- Quick Response -->
              <div class="bg-primary-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-primary-900 mb-2">快速响应</h3>
                <p class="text-primary-700">
                  我们承诺在24小时内回复您的咨询，为您提供专业的解决方案。
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const form = ref({
  name: '',
  email: '',
  phone: '',
  subject: '',
  message: ''
})

const submitForm = () => {
  // 这里可以添加表单提交逻辑
  alert('感谢您的咨询！我们会尽快与您联系。')
  
  // 重置表单
  form.value = {
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  }
}
</script>
