<template>
  <footer class="bg-gray-900 text-white">
    <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <!-- Company Info -->
        <div class="col-span-1 md:col-span-2">
          <h3 class="text-2xl font-bold text-primary-400 mb-4">萌贩机</h3>
          <p class="text-gray-300 mb-4">
            杭州虹色萌机科技有限公司专注于潮玩商品设计和销售、日用百货零售、软件开发和销售、移动端app开发和销售、无人售货机开发和销售。
          </p>
          <p class="text-gray-400 text-sm">
            公司名称：杭州虹色萌机科技有限公司
          </p>
        </div>

        <!-- Quick Links -->
        <div>
          <h4 class="text-lg font-semibold mb-4">快速链接</h4>
          <ul class="space-y-2">
            <li>
              <RouterLink to="/" class="text-gray-300 hover:text-primary-400 transition-colors">
                首页
              </RouterLink>
            </li>
            <li>
              <RouterLink to="/about" class="text-gray-300 hover:text-primary-400 transition-colors">
                关于我们
              </RouterLink>
            </li>
            <li>
              <RouterLink to="/services" class="text-gray-300 hover:text-primary-400 transition-colors">
                服务项目
              </RouterLink>
            </li>
            <li>
              <RouterLink to="/contact" class="text-gray-300 hover:text-primary-400 transition-colors">
                联系我们
              </RouterLink>
            </li>
          </ul>
        </div>

        <!-- Services -->
        <div>
          <h4 class="text-lg font-semibold mb-4">主营业务</h4>
          <ul class="space-y-2 text-gray-300">
            <li>潮玩商品设计和销售</li>
            <li>日用百货零售</li>
            <li>软件开发和销售</li>
            <li>移动端App开发</li>
            <li>无人售货机开发</li>
          </ul>
        </div>
      </div>

      <!-- Bottom Bar -->
      <div class="mt-8 pt-8 border-t border-gray-700">
        <div class="flex flex-col md:flex-row justify-between items-center">
          <p class="text-gray-400 text-sm">
            © 2025 杭州虹色萌机科技有限公司. 保留所有权利.
          </p>
          <div class="mt-4 md:mt-0">
            <p class="text-gray-400 text-sm">
              让科技更有趣，让生活更美好
            </p>
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { RouterLink } from 'vue-router'
</script>
