<template>
  <section
    class="relative bg-gradient-to-br from-primary-50 to-primary-100 overflow-hidden py-16 lg:py-24"
  >
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        <!-- 左侧内容 -->
        <div class="text-center lg:text-left">
          <h1 class="text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl">
            <span class="block">让科技更有趣</span>
            <span class="block text-primary-600">让生活更美好</span>
          </h1>
          <p class="mt-6 text-lg text-gray-600 max-w-2xl mx-auto lg:mx-0">
            杭州虹色萌机科技有限公司专注于潮玩商品设计、软件开发、移动应用和无人售货机等多元化业务，致力于为用户提供创新的科技产品和服务。
          </p>
          <div class="mt-8 flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
            <RouterLink
              to="/services"
              class="inline-flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 transition-colors shadow-lg hover:shadow-xl"
            >
              了解服务
            </RouterLink>
            <RouterLink
              to="/about"
              class="inline-flex items-center justify-center px-8 py-3 border border-primary-600 text-base font-medium rounded-md text-primary-700 bg-white hover:bg-primary-50 transition-colors"
            >
              关于我们
            </RouterLink>
          </div>
        </div>

        <!-- 右侧图标 -->
        <div class="flex items-center justify-center">
          <div class="bg-white rounded-2xl shadow-2xl p-12 text-center">
            <div class="grid grid-cols-2 gap-8">
              <div class="text-6xl">🎮</div>
              <div class="text-6xl">📱</div>
              <div class="text-6xl">🤖</div>
              <div class="text-6xl">🏪</div>
            </div>
            <p class="mt-6 text-lg font-semibold text-gray-700">科技创新 · 品质生活</p>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { RouterLink } from 'vue-router'
</script>
