<template>
  <section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center">
        <h2 class="text-3xl font-extrabold text-gray-900 sm:text-4xl">我们的主营业务</h2>
        <p class="mt-4 text-lg text-gray-600">多元化的业务布局，为客户提供全方位的科技解决方案</p>
      </div>

      <div class="mt-16 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
        <!-- 潮玩商品设计和销售 -->
        <div
          class="group bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 hover:-translate-y-2"
        >
          <div class="p-8">
            <div
              class="flex items-center justify-center h-20 w-20 rounded-xl bg-gradient-to-br from-primary-500 to-primary-600 text-white mx-auto mb-6 group-hover:scale-110 transition-transform duration-300"
            >
              <span class="text-3xl">🎨</span>
            </div>
            <h3 class="text-xl font-bold text-gray-900 text-center mb-4">潮玩商品设计和销售</h3>
            <p class="text-gray-600 text-center leading-relaxed">
              创意设计各类潮流玩具和装饰品，为年轻消费者提供个性化的生活用品。
            </p>
          </div>
        </div>

        <!-- 日用百货零售 -->
        <div
          class="group bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 hover:-translate-y-2"
        >
          <div class="p-8">
            <div
              class="flex items-center justify-center h-20 w-20 rounded-xl bg-gradient-to-br from-green-500 to-green-600 text-white mx-auto mb-6 group-hover:scale-110 transition-transform duration-300"
            >
              <span class="text-3xl">🛍️</span>
            </div>
            <h3 class="text-xl font-bold text-gray-900 text-center mb-4">日用百货零售</h3>
            <p class="text-gray-600 text-center leading-relaxed">
              精选优质日用百货商品，为消费者提供便捷的购物体验和优质的生活用品。
            </p>
          </div>
        </div>

        <!-- 软件开发和销售 -->
        <div
          class="group bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 hover:-translate-y-2"
        >
          <div class="p-8">
            <div
              class="flex items-center justify-center h-20 w-20 rounded-xl bg-gradient-to-br from-purple-500 to-purple-600 text-white mx-auto mb-6 group-hover:scale-110 transition-transform duration-300"
            >
              <span class="text-3xl">💻</span>
            </div>
            <h3 class="text-xl font-bold text-gray-900 text-center mb-4">软件开发和销售</h3>
            <p class="text-gray-600 text-center leading-relaxed">
              专业的软件开发团队，提供定制化软件解决方案，满足企业数字化转型需求。
            </p>
          </div>
        </div>

        <!-- 移动端App开发 -->
        <div
          class="group bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 hover:-translate-y-2"
        >
          <div class="p-8">
            <div
              class="flex items-center justify-center h-20 w-20 rounded-xl bg-gradient-to-br from-orange-500 to-orange-600 text-white mx-auto mb-6 group-hover:scale-110 transition-transform duration-300"
            >
              <span class="text-3xl">📱</span>
            </div>
            <h3 class="text-xl font-bold text-gray-900 text-center mb-4">移动端App开发</h3>
            <p class="text-gray-600 text-center leading-relaxed">
              iOS和Android应用开发，为企业和个人用户打造优质的移动应用体验。
            </p>
          </div>
        </div>

        <!-- 无人售货机开发 -->
        <div
          class="group bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 hover:-translate-y-2"
        >
          <div class="p-8">
            <div
              class="flex items-center justify-center h-20 w-20 rounded-xl bg-gradient-to-br from-red-500 to-red-600 text-white mx-auto mb-6 group-hover:scale-110 transition-transform duration-300"
            >
              <span class="text-3xl">🤖</span>
            </div>
            <h3 class="text-xl font-bold text-gray-900 text-center mb-4">无人售货机开发</h3>
            <p class="text-gray-600 text-center leading-relaxed">
              智能无人售货机研发和销售，推动无人零售行业的创新发展。
            </p>
          </div>
        </div>

        <!-- 无人零售 -->
        <div
          class="group bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 hover:-translate-y-2"
        >
          <div class="p-8">
            <div
              class="flex items-center justify-center h-20 w-20 rounded-xl bg-gradient-to-br from-indigo-500 to-indigo-600 text-white mx-auto mb-6 group-hover:scale-110 transition-transform duration-300"
            >
              <span class="text-3xl">🏪</span>
            </div>
            <h3 class="text-xl font-bold text-gray-900 text-center mb-4">无人零售</h3>
            <p class="text-gray-600 text-center leading-relaxed">
              构建智能化无人零售生态系统，提供24小时便民服务。
            </p>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
// 服务展示组件
</script>
