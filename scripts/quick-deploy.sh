#!/bin/bash

# 一键部署脚本
# 用于快速部署多网站项目

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        print_error "请不要使用root用户运行此脚本"
        exit 1
    fi
}

# 检查必要的命令
check_commands() {
    local commands=("node" "npm" "nginx" "pm2")
    for cmd in "${commands[@]}"; do
        if ! command -v $cmd &> /dev/null; then
            print_error "命令 $cmd 未找到，请先运行 server-init.sh"
            exit 1
        fi
    done
}

# 构建网站
build_sites() {
    print_info "开始构建网站..."
    
    # 构建萌贩机网站
    print_info "构建萌贩机网站..."
    cd sites/moevend
    npm install
    npm run build
    cd ../..
    
    # 构建Graphock网站
    print_info "构建Graphock网站..."
    cd sites/graphock
    npm install
    npm run build
    cd ../..
    
    print_success "所有网站构建完成"
}

# 安装生产依赖
install_production_deps() {
    print_info "安装生产环境依赖..."
    
    cd sites/moevend
    npm install --production
    cd ../graphock
    npm install --production
    cd ../..
    
    print_success "生产依赖安装完成"
}

# 配置Nginx
configure_nginx() {
    print_info "配置Nginx..."
    
    # 复制配置文件
    sudo cp nginx/sites-available/*.conf /etc/nginx/sites-available/
    
    # 创建软链接
    sudo ln -sf /etc/nginx/sites-available/moevend.conf /etc/nginx/sites-enabled/
    sudo ln -sf /etc/nginx/sites-available/graphock.conf /etc/nginx/sites-enabled/
    
    # 测试配置
    if sudo nginx -t; then
        print_success "Nginx配置测试通过"
        sudo systemctl reload nginx
        print_success "Nginx重新加载完成"
    else
        print_error "Nginx配置测试失败"
        exit 1
    fi
}

# 启动PM2服务
start_pm2() {
    print_info "启动PM2服务..."
    
    # 停止现有进程
    pm2 stop all || true
    
    # 启动新进程
    pm2 start pm2/ecosystem.config.js --env production
    
    # 保存配置
    pm2 save
    
    print_success "PM2服务启动完成"
}

# 验证部署
verify_deployment() {
    print_info "验证部署状态..."
    
    # 检查PM2状态
    print_info "PM2进程状态:"
    pm2 status
    
    # 检查Nginx状态
    print_info "Nginx服务状态:"
    sudo systemctl status nginx --no-pager -l
    
    # 检查网站访问
    print_info "检查网站访问..."
    
    if curl -s -I http://localhost:3001 | grep -q "200 OK"; then
        print_success "萌贩机网站 (端口3001) 运行正常"
    else
        print_warning "萌贩机网站 (端口3001) 可能有问题"
    fi
    
    if curl -s -I http://localhost:3002 | grep -q "200 OK"; then
        print_success "Graphock网站 (端口3002) 运行正常"
    else
        print_warning "Graphock网站 (端口3002) 可能有问题"
    fi
}

# 显示帮助信息
show_help() {
    echo "一键部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --build-only     仅构建网站，不部署"
    echo "  --deploy-only    仅部署，不构建 (需要已有构建产物)"
    echo "  --skip-nginx     跳过Nginx配置"
    echo "  --skip-verify    跳过部署验证"
    echo "  --help           显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                # 完整部署流程"
    echo "  $0 --build-only   # 仅构建"
    echo "  $0 --deploy-only  # 仅部署"
}

# 主函数
main() {
    local build_only=false
    local deploy_only=false
    local skip_nginx=false
    local skip_verify=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --build-only)
                build_only=true
                shift
                ;;
            --deploy-only)
                deploy_only=true
                shift
                ;;
            --skip-nginx)
                skip_nginx=true
                shift
                ;;
            --skip-verify)
                skip_verify=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                print_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    print_info "开始一键部署流程..."
    
    # 检查环境
    check_root
    check_commands
    
    # 构建阶段
    if [[ "$deploy_only" != true ]]; then
        build_sites
    fi
    
    # 部署阶段
    if [[ "$build_only" != true ]]; then
        install_production_deps
        
        if [[ "$skip_nginx" != true ]]; then
            configure_nginx
        fi
        
        start_pm2
        
        if [[ "$skip_verify" != true ]]; then
            verify_deployment
        fi
    fi
    
    print_success "部署完成！"
    print_info "您可以通过以下方式访问网站:"
    print_info "- 萌贩机网站: http://localhost:3001"
    print_info "- Graphock网站: http://localhost:3002"
    print_info "- 使用 './scripts/site-manager.sh status' 查看详细状态"
}

# 运行主函数
main "$@"
