#!/bin/bash

# 502错误诊断脚本
# 用于快速诊断和修复Nginx 502 Bad Gateway错误

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

# 检查端口监听
check_port() {
    local port=$1
    local service_name=$2
    
    if netstat -tlnp 2>/dev/null | grep -q ":$port "; then
        print_success "$service_name 正在监听端口 $port"
        return 0
    else
        print_error "$service_name 没有监听端口 $port"
        return 1
    fi
}

# 检查服务状态
check_service() {
    local service=$1
    
    if systemctl is-active --quiet $service; then
        print_success "$service 服务正在运行"
        return 0
    else
        print_error "$service 服务未运行"
        return 1
    fi
}

# 测试HTTP响应
test_http() {
    local url=$1
    local expected_code=$2
    
    local response=$(curl -s -I "$url" 2>/dev/null | head -n1 | awk '{print $2}')
    
    if [ "$response" = "$expected_code" ]; then
        print_success "$url 响应正常 ($response)"
        return 0
    else
        print_error "$url 响应异常 (期望: $expected_code, 实际: $response)"
        return 1
    fi
}

# 主诊断函数
main() {
    print_info "开始502错误诊断..."
    
    # 步骤1: 检查Nginx状态
    print_step "步骤1: 检查Nginx服务状态"
    if check_service nginx; then
        if check_port 80 "Nginx HTTP" && check_port 443 "Nginx HTTPS"; then
            print_success "Nginx服务正常"
        else
            print_warning "Nginx服务运行但端口监听异常"
        fi
    else
        print_error "Nginx服务未运行，尝试启动..."
        sudo systemctl start nginx
        sleep 2
        check_service nginx
    fi
    
    # 步骤2: 检查Node.js应用状态
    print_step "步骤2: 检查Node.js应用状态"
    if check_port 3001 "Node.js应用"; then
        print_success "Node.js应用正在运行"
        
        # 测试本地访问
        if test_http "http://localhost:3001" "200"; then
            print_success "Node.js应用响应正常"
        else
            print_error "Node.js应用无法正常响应"
        fi
    else
        print_error "Node.js应用未运行"
        print_info "尝试启动Node.js应用..."
        
        # 检查项目目录
        if [ -d "/var/www/moevend-website/sites/moevend" ]; then
            cd /var/www/moevend-website/sites/moevend
            
            # 检查dist目录
            if [ -d "dist" ]; then
                print_info "找到dist目录，启动应用..."
                npm run preview &
                sleep 5
                check_port 3001 "Node.js应用"
            else
                print_warning "未找到dist目录，需要构建项目"
                print_info "执行构建..."
                npm install
                npm run build
                npm run preview &
                sleep 5
                check_port 3001 "Node.js应用"
            fi
        else
            print_error "项目目录不存在: /var/www/moevend-website/sites/moevend"
        fi
    fi
    
    # 步骤3: 检查PM2状态
    print_step "步骤3: 检查PM2进程管理"
    if command -v pm2 >/dev/null 2>&1; then
        print_info "PM2进程状态:"
        pm2 status
        
        # 检查是否有moevend相关进程
        if pm2 list | grep -q "moevend"; then
            print_success "发现PM2管理的moevend进程"
        else
            print_warning "未发现PM2管理的moevend进程"
            print_info "建议使用PM2管理应用"
        fi
    else
        print_warning "PM2未安装"
    fi
    
    # 步骤4: 测试网站访问
    print_step "步骤4: 测试网站访问"
    
    # 测试本地访问
    if test_http "http://localhost:3001" "200"; then
        print_success "本地Node.js应用访问正常"
        
        # 测试HTTPS访问
        print_info "测试HTTPS访问..."
        local https_response=$(curl -s -I "https://moevend.cn" 2>/dev/null | head -n1 | awk '{print $2}')
        
        if [ "$https_response" = "200" ]; then
            print_success "HTTPS访问正常"
        elif [ "$https_response" = "502" ]; then
            print_error "仍然是502错误，检查Nginx配置"
        else
            print_warning "HTTPS访问异常，响应码: $https_response"
        fi
    else
        print_error "本地Node.js应用访问失败，这是502错误的根本原因"
    fi
    
    # 步骤5: 检查日志
    print_step "步骤5: 检查错误日志"
    
    print_info "最近的Nginx错误日志:"
    if [ -f "/var/log/nginx/error.log" ]; then
        sudo tail -n 10 /var/log/nginx/error.log
    else
        print_warning "Nginx错误日志文件不存在"
    fi
    
    print_info "最近的Nginx访问日志:"
    if [ -f "/var/log/nginx/access.log" ]; then
        sudo tail -n 5 /var/log/nginx/access.log
    else
        print_warning "Nginx访问日志文件不存在"
    fi
    
    # 步骤6: 提供修复建议
    print_step "步骤6: 修复建议"
    
    if ! check_port 3001 "Node.js应用" >/dev/null 2>&1; then
        print_info "修复建议:"
        echo "1. 进入项目目录: cd /var/www/moevend-website/sites/moevend"
        echo "2. 构建项目: npm run build"
        echo "3. 启动应用: npm run preview"
        echo "4. 或使用PM2管理: pm2 start npm --name 'moevend-website' -- run preview"
    elif ! test_http "http://localhost:3001" "200" >/dev/null 2>&1; then
        print_info "Node.js应用运行但响应异常，建议:"
        echo "1. 检查应用日志"
        echo "2. 重启应用"
        echo "3. 检查依赖是否完整"
    else
        print_info "Node.js应用正常，可能是Nginx配置问题:"
        echo "1. 检查Nginx配置: sudo nginx -t"
        echo "2. 重启Nginx: sudo systemctl restart nginx"
        echo "3. 检查防火墙设置"
    fi
    
    print_info "诊断完成！"
}

# 显示帮助信息
show_help() {
    echo "502错误诊断脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --help           显示此帮助信息"
    echo "  --fix            尝试自动修复常见问题"
    echo ""
    echo "示例:"
    echo "  $0               # 运行诊断"
    echo "  $0 --fix         # 诊断并尝试修复"
}

# 自动修复函数
auto_fix() {
    print_info "尝试自动修复502错误..."
    
    # 停止现有进程
    print_info "停止现有进程..."
    pm2 stop all 2>/dev/null || true
    pm2 delete all 2>/dev/null || true
    
    # 进入项目目录
    cd /var/www/moevend-website/sites/moevend
    
    # 构建项目
    print_info "构建项目..."
    npm install
    npm run build
    
    # 启动应用
    print_info "启动应用..."
    npm run preview &
    
    # 等待启动
    sleep 10
    
    # 重启Nginx
    print_info "重启Nginx..."
    sudo systemctl restart nginx
    
    # 测试
    sleep 5
    print_info "测试修复结果..."
    test_http "https://moevend.cn" "200"
}

# 解析参数
case "${1:-}" in
    --help)
        show_help
        exit 0
        ;;
    --fix)
        auto_fix
        ;;
    *)
        main
        ;;
esac
