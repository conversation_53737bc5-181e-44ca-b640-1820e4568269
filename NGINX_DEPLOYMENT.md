# Nginx 部署配置指南

## 🚨 问题诊断

根据您的错误信息，问题出现在：

1. **协议选项重复定义**：`protocol options redefined for 0.0.0.0:443`
2. **证书文件路径错误**：系统在找 `/etc/ssl/certs/graphock.crt` 但应该使用 Let's Encrypt 证书

## 📋 解决方案

### 步骤 1: 清理现有配置

```bash
# 停止nginx服务
sudo systemctl stop nginx

# 删除可能冲突的配置文件
sudo rm -f /etc/nginx/sites-enabled/default
sudo rm -f /etc/nginx/sites-enabled/moevend.conf
sudo rm -f /etc/nginx/sites-enabled/graphock.conf

# 清理sites-available中的旧配置
sudo rm -f /etc/nginx/sites-available/moevend.conf
sudo rm -f /etc/nginx/sites-available/graphock.conf
```

### 步骤 2: 创建正确的 Nginx 配置

#### 2.1 创建 moevend.cn 配置文件

```bash
sudo nano /etc/nginx/sites-available/moevend.conf
```

**配置内容**：

```nginx
# HTTP重定向到HTTPS
server {
    listen 80;
    server_name moevend.cn www.moevend.cn;
    return 301 https://$server_name$request_uri;
}

# HTTPS配置
server {
    listen 443 ssl http2;
    server_name moevend.cn www.moevend.cn;

    # SSL证书配置 (Let's Encrypt)
    ssl_certificate /etc/letsencrypt/live/moevend.cn/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/moevend.cn/privkey.pem;

    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";

    # 反向代理到Node.js应用
    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        proxy_pass http://localhost:3001;
        proxy_set_header Host $host;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

#### 2.2 如果您有 graphock.cn 域名，创建 graphock 配置

```bash
sudo nano /etc/nginx/sites-available/graphock.conf
```

**配置内容**：

```nginx
# HTTP重定向到HTTPS
server {
    listen 80;
    server_name graphock.cn www.graphock.cn;
    return 301 https://$server_name$request_uri;
}

# HTTPS配置
server {
    listen 443 ssl http2;
    server_name graphock.cn www.graphock.cn;

    # SSL证书配置 (需要为graphock.cn申请证书)
    ssl_certificate /etc/letsencrypt/live/graphock.cn/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/graphock.cn/privkey.pem;

    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";

    # 反向代理到Node.js应用
    location / {
        proxy_pass http://localhost:3002;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        proxy_pass http://localhost:3002;
        proxy_set_header Host $host;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### 步骤 3: 启用配置

```bash
# 启用moevend配置
sudo ln -sf /etc/nginx/sites-available/moevend.conf /etc/nginx/sites-enabled/

# 如果有graphock域名且已申请证书，启用graphock配置
# sudo ln -sf /etc/nginx/sites-available/graphock.conf /etc/nginx/sites-enabled/
```

### 步骤 4: 测试配置

```bash
# 测试nginx配置
sudo nginx -t
```

**如果测试通过，应该看到**：

```
nginx: the configuration file /etc/nginx/nginx.conf syntax is ok
nginx: configuration file /etc/nginx/nginx.conf test is successful
```

### 步骤 5: 启动服务

```bash
# 启动nginx
sudo systemctl start nginx

# 设置开机自启
sudo systemctl enable nginx

# 检查状态
sudo systemctl status nginx
```

## 🔧 故障排除

### 如果仍然报错，按以下步骤检查：

#### 1. 检查证书文件是否存在

```bash
ls -la /etc/letsencrypt/live/moevend.cn/
```

#### 2. 检查 nginx 主配置文件

```bash
sudo nano /etc/nginx/nginx.conf
```

确保包含以下内容：

```nginx
http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 包含站点配置
    include /etc/nginx/sites-enabled/*;
}
```

#### 3. 检查端口占用

```bash
sudo netstat -tlnp | grep :443
sudo netstat -tlnp | grep :80
```

#### 4. 查看详细错误日志

```bash
sudo tail -f /var/log/nginx/error.log
```

### 如果需要为 graphock.cn 申请 SSL 证书

```bash
# 申请graphock.cn的SSL证书
sudo certbot --nginx -d graphock.cn -d www.graphock.cn

# 然后启用graphock配置
sudo ln -sf /etc/nginx/sites-available/graphock.conf /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## ✅ 验证部署

```bash
# 检查网站访问
curl -I https://moevend.cn
curl -I http://moevend.cn  # 应该重定向到https

# 检查SSL证书
openssl s_client -connect moevend.cn:443 -servername moevend.cn
```

## 🚨 502 Bad Gateway 错误解决方案

如果您看到 `HTTP/2 502` 错误，这表示 Nginx 无法连接到后端的 Node.js 应用。

### 🚀 快速诊断 (推荐)

我们提供了一个智能诊断脚本，可以自动检测端口并修复 502 问题：

```bash
# 进入项目目录
cd /var/www/moevend-website

# 运行诊断脚本 (自动检测端口)
./scripts/diagnose-502.sh

# 或者尝试自动修复 (推荐)
./scripts/diagnose-502.sh --fix
```

**脚本功能**：

- ✅ 自动检测 Node.js 应用实际运行的端口
- ✅ 自动更新 Nginx 配置以匹配正确端口
- ✅ 处理 Vite 默认端口 4173 而非 3001 的情况
- ✅ 智能重启和验证服务

**常见端口说明**：

- `3001`: 项目配置的目标端口
- `4173`: Vite 预览服务器的默认端口
- `3000`, `5173`: 其他常见的开发端口

### 📋 手动诊断步骤

如果您想手动排查问题，请按以下步骤进行：

### 步骤 1: 检查 Node.js 应用是否运行

```bash
# 检查端口3001是否有进程在监听
sudo netstat -tlnp | grep :3001

# 检查PM2进程状态
pm2 status

# 如果没有进程，检查PM2日志
pm2 logs
```

### 步骤 2: 启动 Node.js 应用

如果应用没有运行，需要先启动：

```bash
# 进入项目目录
cd /var/www/moevend-website

# 启动moevend网站 (方法1: 使用PM2)
cd sites/moevend
pm2 start npm --name "moevend-website" -- run preview

# 或者 (方法2: 直接运行)
npm run preview
```

### 步骤 3: 检查应用是否正常响应

```bash
# 测试本地端口3001
curl -I http://localhost:3001

# 如果正常，应该看到类似输出：
# HTTP/1.1 200 OK
# Content-Type: text/html
```

### 步骤 4: 如果应用无法启动

#### 4.1 检查依赖是否安装

```bash
cd /var/www/moevend-website/sites/moevend

# 检查是否有dist目录
ls -la dist/

# 如果没有dist目录，需要构建
npm run build

# 安装生产依赖
npm install --production
```

#### 4.2 检查端口冲突

```bash
# 检查3001端口是否被其他进程占用
sudo lsof -i :3001

# 如果被占用，杀掉进程
sudo kill -9 <PID>
```

#### 4.3 手动测试应用

```bash
cd /var/www/moevend-website/sites/moevend

# 手动启动应用查看错误
npm run preview

# 查看详细错误信息
```

### 步骤 5: 修复常见问题

#### 5.1 如果提示"dist 目录不存在"

```bash
cd /var/www/moevend-website/sites/moevend

# 重新构建项目
npm install
npm run build

# 然后启动预览
npm run preview
```

#### 5.2 如果提示"权限错误"

```bash
# 修复文件权限
sudo chown -R $USER:$USER /var/www/moevend-website
chmod -R 755 /var/www/moevend-website
```

#### 5.3 如果提示"模块未找到"

```bash
cd /var/www/moevend-website/sites/moevend

# 清理并重新安装依赖
rm -rf node_modules package-lock.json
npm install
npm run build
npm run preview
```

### 步骤 6: 使用 PM2 管理应用 (推荐)

```bash
# 停止所有PM2进程
pm2 stop all
pm2 delete all

# 进入项目目录
cd /var/www/moevend-website

# 使用项目中的PM2配置启动
pm2 start pm2/ecosystem.config.js --env production

# 保存PM2配置
pm2 save

# 设置开机自启
pm2 startup
```

### 步骤 7: 验证修复

```bash
# 1. 检查PM2状态
pm2 status

# 2. 检查端口监听
sudo netstat -tlnp | grep :3001

# 3. 测试本地访问
curl -I http://localhost:3001

# 4. 测试HTTPS访问
curl -I https://moevend.cn

# 5. 如果仍有问题，查看nginx错误日志
sudo tail -f /var/log/nginx/error.log
```

### 步骤 8: 完整的重启流程

如果以上步骤都不行，执行完整重启：

```bash
# 1. 停止所有服务
sudo systemctl stop nginx
pm2 stop all
pm2 delete all

# 2. 重新构建应用
cd /var/www/moevend-website/sites/moevend
npm run build

# 3. 启动应用
npm run preview &

# 4. 等待几秒，然后测试
sleep 5
curl -I http://localhost:3001

# 5. 如果本地测试成功，启动nginx
sudo systemctl start nginx

# 6. 最终测试
curl -I https://moevend.cn
```

### 🔍 调试技巧

#### 查看实时日志

```bash
# 查看nginx错误日志
sudo tail -f /var/log/nginx/error.log

# 查看nginx访问日志
sudo tail -f /var/log/nginx/access.log

# 查看PM2日志
pm2 logs --lines 50

# 查看系统日志
sudo journalctl -u nginx -f
```

#### 常见错误信息及解决方案

1. **"Connection refused"** - Node.js 应用未启动
2. **"No such file or directory"** - dist 目录不存在，需要构建
3. **"Permission denied"** - 文件权限问题
4. **"Port already in use"** - 端口被占用

### ⚠️ 重要提醒

1. **确保应用先启动**：Nginx 只是代理，后端应用必须先运行
2. **检查防火墙**：确保 3001 端口在内部可访问
3. **使用 PM2 管理**：避免应用意外停止
4. **定期检查日志**：及时发现和解决问题

## 📝 注意事项

1. **只配置已有证书的域名**：如果只有 moevend.cn 的证书，就只配置 moevend.cn
2. **避免端口冲突**：确保 443 端口没有被其他服务占用
3. **防火墙设置**：确保 80 和 443 端口已开放
4. **证书自动续期**：Let's Encrypt 证书会自动续期

## 🚀 针对您当前问题的快速修复

根据您的错误信息，问题是证书路径不匹配。您已经有了 moevend.cn 的证书，但配置文件在找错误的路径。

### 立即修复步骤：

```bash
# 1. 停止nginx
sudo systemctl stop nginx

# 2. 清理所有sites-enabled配置
sudo rm -f /etc/nginx/sites-enabled/*

# 3. 使用项目中已修复的配置文件
cd /var/www/moevend-website

# 4. 复制修复后的moevend配置 (只配置已有证书的域名)
sudo cp nginx/sites-available/moevend.conf /etc/nginx/sites-available/
sudo ln -sf /etc/nginx/sites-available/moevend.conf /etc/nginx/sites-enabled/

# 5. 测试配置
sudo nginx -t
```

**如果测试通过，继续：**

```bash
# 6. 启动nginx
sudo systemctl start nginx
sudo systemctl enable nginx

# 7. 验证网站
curl -I https://moevend.cn
```

### 如果您还想配置 graphock.cn：

```bash
# 1. 先为graphock.cn申请SSL证书
sudo certbot --nginx -d graphock.cn -d www.graphock.cn

# 2. 然后启用graphock配置
sudo cp nginx/sites-available/graphock.conf /etc/nginx/sites-available/
sudo ln -sf /etc/nginx/sites-available/graphock.conf /etc/nginx/sites-enabled/

# 3. 测试并重启
sudo nginx -t && sudo systemctl reload nginx
```

### 如果只想配置 moevend.cn (推荐)：

```bash
# 1. 停止nginx
sudo systemctl stop nginx

# 2. 清理配置
sudo rm -f /etc/nginx/sites-enabled/*

# 3. 只配置moevend.cn (已有证书)
sudo tee /etc/nginx/sites-available/moevend.conf > /dev/null << 'EOF'
server {
    listen 80;
    server_name moevend.cn www.moevend.cn;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name moevend.cn www.moevend.cn;

    ssl_certificate /etc/letsencrypt/live/moevend.cn/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/moevend.cn/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
EOF

# 4. 启用配置
sudo ln -sf /etc/nginx/sites-available/moevend.conf /etc/nginx/sites-enabled/

# 5. 测试并启动
sudo nginx -t && sudo systemctl start nginx
```
